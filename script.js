// Wait for the DOM to be fully loaded
document.addEventListener('DOMContentLoaded', function() {
    console.log('DOM loaded, starting location detection...');
    // Get user's location and update the heading
    getUserLocation();
    // Mobile menu toggle
    const mobileMenuButton = document.getElementById('mobile-menu-button');
    const mobileMenu = document.getElementById('mobile-menu');

    if (mobileMenuButton && mobileMenu) {
        mobileMenuButton.addEventListener('click', function() {
            mobileMenu.classList.toggle('hidden');
        });

        // Close mobile menu when clicking on a link
        const mobileMenuLinks = mobileMenu.querySelectorAll('a');
        mobileMenuLinks.forEach(link => {
            link.addEventListener('click', function() {
                mobileMenu.classList.add('hidden');
            });
        });
    }

    // Form submission handling - Netlify forms handle submission automatically
    // We can add any additional client-side enhancements here if needed
    const forms = document.querySelectorAll('form[netlify]');

    forms.forEach(form => {
        form.addEventListener('submit', function() {
            // Let Netlify handle the form submission
            // We can add loading states or other UI enhancements here
            const submitButton = form.querySelector('button[type="submit"]');
            if (submitButton) {
                submitButton.textContent = 'Submitting...';
                submitButton.disabled = true;
            }
        });
    });

    // Smooth scrolling for anchor links
    document.querySelectorAll('a[href^="#"]').forEach(anchor => {
        anchor.addEventListener('click', function(e) {
            e.preventDefault();

            const targetId = this.getAttribute('href');
            const targetElement = document.querySelector(targetId);

            if (targetElement) {
                window.scrollTo({
                    top: targetElement.offsetTop - 80, // Offset for fixed header
                    behavior: 'smooth'
                });
            }
        });
    });

    // Add animation classes on scroll
    const animateOnScroll = function() {
        const elements = document.querySelectorAll('.animate-on-scroll');

        elements.forEach(element => {
            const elementPosition = element.getBoundingClientRect().top;
            const windowHeight = window.innerHeight;

            if (elementPosition < windowHeight - 100) {
                element.classList.add('animate-fadeIn');
            }
        });
    };

    // Run once on page load
    animateOnScroll();

    // Run on scroll
    window.addEventListener('scroll', animateOnScroll);
});

// Function to get user's location using multiple APIs
function getUserLocation() {
    // Check if the user-state element exists (only on homepage)
    const userStateElement = document.getElementById('user-state');
    if (!userStateElement) {
        return; // Exit if not on homepage
    }

    console.log('Starting location detection...');

    // Try ipapi.co first (supports HTTPS and is free)
    fetch('https://ipapi.co/json/')
        .then(response => {
            console.log('ipapi.co response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('ipapi.co data:', data);
            if (data.region) {
                // Update the heading with the user's state
                userStateElement.textContent = data.region;
                console.log('Updated location to:', data.region);
            } else {
                // Try backup API
                tryBackupAPI(userStateElement);
            }
        })
        .catch(error => {
            console.log('ipapi.co failed:', error);
            // Try backup API
            tryBackupAPI(userStateElement);
        });
}

// Backup function to try ip-api.com with HTTP (for local testing)
function tryBackupAPI(userStateElement) {
    console.log('Trying backup API...');

    // For local file:// testing, we can try the HTTP endpoint
    fetch('http://ip-api.com/json/')
        .then(response => {
            console.log('ip-api.com response status:', response.status);
            return response.json();
        })
        .then(data => {
            console.log('ip-api.com data:', data);
            if (data.status === 'success' && data.regionName) {
                // Update the heading with the user's state
                userStateElement.textContent = data.regionName;
                console.log('Updated location to:', data.regionName);
            } else {
                // Final fallback
                userStateElement.textContent = 'Your Area';
                console.log('Using fallback text');
            }
        })
        .catch(error => {
            console.log('Backup API also failed:', error);
            // Final fallback to default text
            userStateElement.textContent = 'Your Area';
            console.log('Using fallback text due to error');
        });
}
